"""
Tests for Ollama API schemas.

This module tests the Pydantic models for Ollama API format validation,
serialization, and deserialization.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from app.api.schemas.ollama_schemas import (
    OllamaMessage,
    OllamaChatRequest,
    OllamaResponseMessage,
    OllamaChatResponse,
    OllamaStreamResponse,
    OllamaErrorResponse,
)


class TestOllamaMessage:
    """Test cases for OllamaMessage schema."""
    
    def test_valid_message(self):
        """Test creating a valid Ollama message."""
        message = OllamaMessage(
            role="user",
            content="Hello, how are you?"
        )
        
        assert message.role == "user"
        assert message.content == "Hello, how are you?"
        assert message.images is None
    
    def test_message_with_images(self):
        """Test creating a message with images."""
        message = OllamaMessage(
            role="user",
            content="What's in this image?",
            images=["base64encodedimage1", "base64encodedimage2"]
        )
        
        assert message.role == "user"
        assert message.content == "What's in this image?"
        assert message.images == ["base64encodedimage1", "base64encodedimage2"]
    
    def test_invalid_role(self):
        """Test that invalid roles are rejected."""
        with pytest.raises(ValidationError) as exc_info:
            OllamaMessage(
                role="invalid_role",
                content="Hello"
            )
        
        assert "Input should be 'system', 'user' or 'assistant'" in str(exc_info.value)
    
    def test_missing_content(self):
        """Test that missing content is rejected."""
        with pytest.raises(ValidationError) as exc_info:
            OllamaMessage(role="user")
        
        assert "Field required" in str(exc_info.value)


class TestOllamaChatRequest:
    """Test cases for OllamaChatRequest schema."""
    
    def test_minimal_request(self):
        """Test creating a minimal chat request."""
        messages = [
            OllamaMessage(role="user", content="Hello")
        ]
        
        request = OllamaChatRequest(
            model="llama3",
            messages=messages
        )
        
        assert request.model == "llama3"
        assert len(request.messages) == 1
        assert request.stream is False  # Default value
        assert request.format is None
        assert request.options is None
    
    def test_full_request(self):
        """Test creating a request with all optional fields."""
        messages = [
            OllamaMessage(role="system", content="You are a helpful assistant"),
            OllamaMessage(role="user", content="Hello")
        ]
        
        request = OllamaChatRequest(
            model="llama3",
            messages=messages,
            stream=True,
            format="json",
            options={"temperature": 0.7, "max_tokens": 100},
            template="Custom template",
            system="System message",
            raw=True,
            keep_alive="5m"
        )
        
        assert request.model == "llama3"
        assert len(request.messages) == 2
        assert request.stream is True
        assert request.format == "json"
        assert request.options == {"temperature": 0.7, "max_tokens": 100}
        assert request.template == "Custom template"
        assert request.system == "System message"
        assert request.raw is True
        assert request.keep_alive == "5m"
    
    def test_missing_model(self):
        """Test that missing model is rejected."""
        messages = [OllamaMessage(role="user", content="Hello")]
        
        with pytest.raises(ValidationError) as exc_info:
            OllamaChatRequest(messages=messages)
        
        assert "Field required" in str(exc_info.value)
    
    def test_empty_messages(self):
        """Test that empty messages list is allowed (validation happens at router level)."""
        request = OllamaChatRequest(
            model="llama3",
            messages=[]
        )
        
        assert request.model == "llama3"
        assert len(request.messages) == 0


class TestOllamaResponseMessage:
    """Test cases for OllamaResponseMessage schema."""
    
    def test_valid_response_message(self):
        """Test creating a valid response message."""
        message = OllamaResponseMessage(
            role="assistant",
            content="Hello! I'm doing well, thank you for asking."
        )
        
        assert message.role == "assistant"
        assert message.content == "Hello! I'm doing well, thank you for asking."
    
    def test_default_role(self):
        """Test that role defaults to 'assistant'."""
        message = OllamaResponseMessage(
            content="Hello!"
        )
        
        assert message.role == "assistant"


class TestOllamaChatResponse:
    """Test cases for OllamaChatResponse schema."""
    
    def test_minimal_response(self):
        """Test creating a minimal chat response."""
        message = OllamaResponseMessage(content="Hello!")
        created_at = datetime.now()
        
        response = OllamaChatResponse(
            model="llama3",
            created_at=created_at,
            message=message
        )
        
        assert response.model == "llama3"
        assert response.created_at == created_at
        assert response.message.content == "Hello!"
        assert response.done is True  # Default value
        assert response.total_duration is None
    
    def test_full_response(self):
        """Test creating a response with all metadata fields."""
        message = OllamaResponseMessage(content="Hello!")
        created_at = datetime.now()
        
        response = OllamaChatResponse(
            model="llama3",
            created_at=created_at,
            message=message,
            done=True,
            total_duration=1000000000,  # 1 second in nanoseconds
            load_duration=100000000,   # 0.1 seconds
            prompt_eval_count=10,
            prompt_eval_duration=50000000,
            eval_count=20,
            eval_duration=200000000
        )
        
        assert response.model == "llama3"
        assert response.total_duration == 1000000000
        assert response.load_duration == 100000000
        assert response.prompt_eval_count == 10
        assert response.prompt_eval_duration == 50000000
        assert response.eval_count == 20
        assert response.eval_duration == 200000000


class TestOllamaStreamResponse:
    """Test cases for OllamaStreamResponse schema."""
    
    def test_stream_chunk(self):
        """Test creating a streaming response chunk."""
        message = OllamaResponseMessage(content="Hello")
        created_at = datetime.now()
        
        chunk = OllamaStreamResponse(
            model="llama3",
            created_at=created_at,
            message=message,
            done=False
        )
        
        assert chunk.model == "llama3"
        assert chunk.created_at == created_at
        assert chunk.message.content == "Hello"
        assert chunk.done is False
        assert chunk.total_duration is None
    
    def test_final_stream_chunk(self):
        """Test creating a final streaming response chunk with metadata."""
        message = OllamaResponseMessage(content="")
        created_at = datetime.now()
        
        chunk = OllamaStreamResponse(
            model="llama3",
            created_at=created_at,
            message=message,
            done=True,
            total_duration=1000000000,
            eval_count=50
        )
        
        assert chunk.done is True
        assert chunk.total_duration == 1000000000
        assert chunk.eval_count == 50


class TestOllamaErrorResponse:
    """Test cases for OllamaErrorResponse schema."""
    
    def test_error_response(self):
        """Test creating an error response."""
        error = OllamaErrorResponse(
            error="Model not found"
        )
        
        assert error.error == "Model not found"
    
    def test_missing_error(self):
        """Test that missing error field is rejected."""
        with pytest.raises(ValidationError) as exc_info:
            OllamaErrorResponse()
        
        assert "Field required" in str(exc_info.value)


class TestSerialization:
    """Test JSON serialization and deserialization."""
    
    def test_request_serialization(self):
        """Test that requests can be serialized to JSON."""
        messages = [
            OllamaMessage(role="user", content="Hello")
        ]
        
        request = OllamaChatRequest(
            model="llama3",
            messages=messages,
            stream=False
        )
        
        # Test model_dump (Pydantic v2)
        data = request.model_dump()
        assert data["model"] == "llama3"
        assert len(data["messages"]) == 1
        assert data["messages"][0]["role"] == "user"
        assert data["stream"] is False
    
    def test_response_deserialization(self):
        """Test that responses can be created from JSON data."""
        data = {
            "model": "llama3",
            "created_at": "2024-01-01T12:00:00",
            "message": {
                "role": "assistant",
                "content": "Hello!"
            },
            "done": True
        }
        
        response = OllamaChatResponse(**data)
        assert response.model == "llama3"
        assert response.message.content == "Hello!"
        assert response.done is True
