"""
MedusaXD/OpenAI API format schemas.

This module contains Pydantic models that represent the OpenAI-compatible API format
used by the MedusaXD backend service.
"""

from typing import List, Optional, Dict, Any, Literal, Union

from pydantic import BaseModel, Field


class MedusaMessage(BaseModel):
    """
    Represents a message in OpenAI chat format.
    
    Supports various roles and content types including multimodal content.
    """
    
    role: Literal["system", "user", "assistant", "tool", "function"] = Field(
        ...,
        description="The role of the message sender"
    )
    
    content: Union[str, List[Dict[str, Any]], None] = Field(
        ...,
        description="The message content (string or multimodal parts)"
    )
    
    name: Optional[str] = Field(
        None,
        description="Optional name for the message sender"
    )
    
    tool_calls: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Tool calls made by the assistant"
    )
    
    tool_call_id: Optional[str] = Field(
        None,
        description="ID of the tool call being responded to"
    )


class MedusaChatRequest(BaseModel):
    """
    Represents an OpenAI-compatible chat completion request.
    
    This matches the format expected by the MedusaXD backend's
    /v1/chat/completions endpoint.
    """
    
    model: str = Field(
        ...,
        description="The model to use for the chat completion"
    )
    
    messages: List[MedusaMessage] = Field(
        ...,
        description="List of messages in the conversation"
    )
    
    stream: Optional[bool] = Field(
        False,
        description="Whether to stream the response"
    )
    
    temperature: Optional[float] = Field(
        None,
        description="Sampling temperature (0-2)",
        ge=0.0,
        le=2.0
    )
    
    max_tokens: Optional[int] = Field(
        None,
        description="Maximum number of tokens to generate",
        gt=0
    )
    
    top_p: Optional[float] = Field(
        None,
        description="Nucleus sampling parameter",
        ge=0.0,
        le=1.0
    )
    
    frequency_penalty: Optional[float] = Field(
        None,
        description="Frequency penalty (-2.0 to 2.0)",
        ge=-2.0,
        le=2.0
    )
    
    presence_penalty: Optional[float] = Field(
        None,
        description="Presence penalty (-2.0 to 2.0)",
        ge=-2.0,
        le=2.0
    )
    
    stop: Optional[Union[str, List[str]]] = Field(
        None,
        description="Stop sequences"
    )
    
    n: Optional[int] = Field(
        None,
        description="Number of completions to generate",
        gt=0
    )
    
    user: Optional[str] = Field(
        None,
        description="User identifier for tracking"
    )


class MedusaUsage(BaseModel):
    """
    Represents token usage information in the response.
    """
    
    prompt_tokens: int = Field(
        ...,
        description="Number of tokens in the prompt"
    )
    
    completion_tokens: int = Field(
        ...,
        description="Number of tokens in the completion"
    )
    
    total_tokens: int = Field(
        ...,
        description="Total number of tokens used"
    )


class MedusaChoice(BaseModel):
    """
    Represents a single choice in the chat completion response.
    """
    
    index: int = Field(
        ...,
        description="Index of this choice"
    )
    
    message: MedusaMessage = Field(
        ...,
        description="The generated message"
    )
    
    finish_reason: Optional[Literal["stop", "length", "content_filter", "tool_calls", "function_call"]] = Field(
        None,
        description="Reason why the generation stopped"
    )
    
    logprobs: Optional[Dict[str, Any]] = Field(
        None,
        description="Log probabilities for tokens"
    )


class MedusaChatResponse(BaseModel):
    """
    Represents an OpenAI-compatible chat completion response.
    
    This matches the format returned by the MedusaXD backend's
    /v1/chat/completions endpoint.
    """
    
    id: str = Field(
        ...,
        description="Unique identifier for the completion"
    )
    
    object: Literal["chat.completion"] = Field(
        "chat.completion",
        description="Object type"
    )
    
    created: int = Field(
        ...,
        description="Unix timestamp when the completion was created"
    )
    
    model: str = Field(
        ...,
        description="The model used for the completion"
    )
    
    choices: List[MedusaChoice] = Field(
        ...,
        description="List of completion choices"
    )
    
    usage: Optional[MedusaUsage] = Field(
        None,
        description="Token usage information"
    )
    
    system_fingerprint: Optional[str] = Field(
        None,
        description="System fingerprint"
    )


class MedusaStreamChoice(BaseModel):
    """
    Represents a choice in a streaming response chunk.
    """
    
    index: int = Field(
        ...,
        description="Index of this choice"
    )
    
    delta: Dict[str, Any] = Field(
        ...,
        description="Delta containing the new content"
    )
    
    finish_reason: Optional[str] = Field(
        None,
        description="Reason why the generation stopped"
    )


class MedusaStreamResponse(BaseModel):
    """
    Represents a single chunk in an OpenAI-compatible streaming response.
    """
    
    id: str = Field(
        ...,
        description="Unique identifier for the completion"
    )
    
    object: Literal["chat.completion.chunk"] = Field(
        "chat.completion.chunk",
        description="Object type"
    )
    
    created: int = Field(
        ...,
        description="Unix timestamp when the chunk was created"
    )
    
    model: str = Field(
        ...,
        description="The model generating the response"
    )
    
    choices: List[MedusaStreamChoice] = Field(
        ...,
        description="List of choice deltas"
    )
    
    system_fingerprint: Optional[str] = Field(
        None,
        description="System fingerprint"
    )


class MedusaErrorResponse(BaseModel):
    """
    Represents an error response from the MedusaXD backend.
    """
    
    error: Dict[str, Any] = Field(
        ...,
        description="Error details"
    )


class MedusaErrorDetail(BaseModel):
    """
    Represents detailed error information.
    """
    
    message: str = Field(
        ...,
        description="Error message"
    )
    
    type: str = Field(
        ...,
        description="Error type"
    )
    
    param: Optional[str] = Field(
        None,
        description="Parameter that caused the error"
    )
    
    code: Optional[str] = Field(
        None,
        description="Error code"
    )
