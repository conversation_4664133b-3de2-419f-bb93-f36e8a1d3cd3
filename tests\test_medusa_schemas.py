"""
Tests for MedusaXD/OpenAI API schemas.

This module tests the Pydantic models for OpenAI-compatible API format
validation, serialization, and deserialization.
"""

import pytest
from pydantic import ValidationError

from app.api.schemas.medusa_schemas import (
    MedusaMessage,
    MedusaChatRequest,
    MedusaUsage,
    MedusaChoice,
    MedusaChatResponse,
    MedusaStreamChoice,
    MedusaStreamResponse,
    MedusaErrorResponse,
    MedusaErrorDetail,
)


class TestMedusaMessage:
    """Test cases for MedusaMessage schema."""
    
    def test_simple_message(self):
        """Test creating a simple text message."""
        message = MedusaMessage(
            role="user",
            content="Hello, how are you?"
        )
        
        assert message.role == "user"
        assert message.content == "Hello, how are you?"
        assert message.name is None
        assert message.tool_calls is None
    
    def test_multimodal_message(self):
        """Test creating a multimodal message with content parts."""
        content_parts = [
            {"type": "text", "text": "What's in this image?"},
            {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
        ]
        
        message = MedusaMessage(
            role="user",
            content=content_parts
        )
        
        assert message.role == "user"
        assert isinstance(message.content, list)
        assert len(message.content) == 2
    
    def test_assistant_with_tool_calls(self):
        """Test creating an assistant message with tool calls."""
        tool_calls = [
            {
                "id": "call_123",
                "type": "function",
                "function": {"name": "get_weather", "arguments": '{"location": "NYC"}'}
            }
        ]
        
        message = MedusaMessage(
            role="assistant",
            content=None,
            tool_calls=tool_calls
        )
        
        assert message.role == "assistant"
        assert message.content is None
        assert message.tool_calls == tool_calls
    
    def test_invalid_role(self):
        """Test that invalid roles are rejected."""
        with pytest.raises(ValidationError) as exc_info:
            MedusaMessage(
                role="invalid_role",
                content="Hello"
            )
        
        assert "Input should be" in str(exc_info.value)


class TestMedusaChatRequest:
    """Test cases for MedusaChatRequest schema."""
    
    def test_minimal_request(self):
        """Test creating a minimal chat request."""
        messages = [
            MedusaMessage(role="user", content="Hello")
        ]
        
        request = MedusaChatRequest(
            model="gpt-4o-mini",
            messages=messages
        )
        
        assert request.model == "gpt-4o-mini"
        assert len(request.messages) == 1
        assert request.stream is False  # Default value
        assert request.temperature is None
        assert request.max_tokens is None
    
    def test_full_request(self):
        """Test creating a request with all optional parameters."""
        messages = [
            MedusaMessage(role="system", content="You are helpful"),
            MedusaMessage(role="user", content="Hello")
        ]
        
        request = MedusaChatRequest(
            model="gpt-4",
            messages=messages,
            stream=True,
            temperature=0.7,
            max_tokens=100,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.2,
            stop=["END"],
            n=1,
            user="user123"
        )
        
        assert request.model == "gpt-4"
        assert len(request.messages) == 2
        assert request.stream is True
        assert request.temperature == 0.7
        assert request.max_tokens == 100
        assert request.top_p == 0.9
        assert request.frequency_penalty == 0.1
        assert request.presence_penalty == 0.2
        assert request.stop == ["END"]
        assert request.n == 1
        assert request.user == "user123"
    
    def test_temperature_validation(self):
        """Test temperature parameter validation."""
        messages = [MedusaMessage(role="user", content="Hello")]
        
        # Valid temperature
        request = MedusaChatRequest(
            model="gpt-4",
            messages=messages,
            temperature=1.0
        )
        assert request.temperature == 1.0
        
        # Invalid temperature (too high)
        with pytest.raises(ValidationError):
            MedusaChatRequest(
                model="gpt-4",
                messages=messages,
                temperature=3.0
            )
        
        # Invalid temperature (negative)
        with pytest.raises(ValidationError):
            MedusaChatRequest(
                model="gpt-4",
                messages=messages,
                temperature=-0.1
            )
    
    def test_max_tokens_validation(self):
        """Test max_tokens parameter validation."""
        messages = [MedusaMessage(role="user", content="Hello")]
        
        # Valid max_tokens
        request = MedusaChatRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=100
        )
        assert request.max_tokens == 100
        
        # Invalid max_tokens (zero)
        with pytest.raises(ValidationError):
            MedusaChatRequest(
                model="gpt-4",
                messages=messages,
                max_tokens=0
            )


class TestMedusaUsage:
    """Test cases for MedusaUsage schema."""
    
    def test_usage_creation(self):
        """Test creating usage information."""
        usage = MedusaUsage(
            prompt_tokens=10,
            completion_tokens=20,
            total_tokens=30
        )
        
        assert usage.prompt_tokens == 10
        assert usage.completion_tokens == 20
        assert usage.total_tokens == 30
    
    def test_missing_fields(self):
        """Test that all fields are required."""
        with pytest.raises(ValidationError):
            MedusaUsage(prompt_tokens=10, completion_tokens=20)


class TestMedusaChoice:
    """Test cases for MedusaChoice schema."""
    
    def test_choice_creation(self):
        """Test creating a choice."""
        message = MedusaMessage(role="assistant", content="Hello!")
        
        choice = MedusaChoice(
            index=0,
            message=message,
            finish_reason="stop"
        )
        
        assert choice.index == 0
        assert choice.message.content == "Hello!"
        assert choice.finish_reason == "stop"
        assert choice.logprobs is None
    
    def test_choice_with_logprobs(self):
        """Test creating a choice with log probabilities."""
        message = MedusaMessage(role="assistant", content="Hello!")
        logprobs = {"tokens": ["Hello", "!"], "token_logprobs": [-0.1, -0.2]}
        
        choice = MedusaChoice(
            index=0,
            message=message,
            finish_reason="stop",
            logprobs=logprobs
        )
        
        assert choice.logprobs == logprobs


class TestMedusaChatResponse:
    """Test cases for MedusaChatResponse schema."""
    
    def test_minimal_response(self):
        """Test creating a minimal chat response."""
        message = MedusaMessage(role="assistant", content="Hello!")
        choice = MedusaChoice(index=0, message=message, finish_reason="stop")
        
        response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4o-mini",
            choices=[choice]
        )
        
        assert response.id == "chatcmpl-123"
        assert response.object == "chat.completion"  # Default value
        assert response.created == 1234567890
        assert response.model == "gpt-4o-mini"
        assert len(response.choices) == 1
        assert response.usage is None
    
    def test_full_response(self):
        """Test creating a response with all fields."""
        message = MedusaMessage(role="assistant", content="Hello!")
        choice = MedusaChoice(index=0, message=message, finish_reason="stop")
        usage = MedusaUsage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        
        response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[choice],
            usage=usage,
            system_fingerprint="fp_123"
        )
        
        assert response.usage.total_tokens == 15
        assert response.system_fingerprint == "fp_123"


class TestMedusaStreamResponse:
    """Test cases for MedusaStreamResponse schema."""
    
    def test_stream_chunk(self):
        """Test creating a streaming response chunk."""
        delta = {"role": "assistant", "content": "Hello"}
        choice = MedusaStreamChoice(index=0, delta=delta, finish_reason=None)
        
        chunk = MedusaStreamResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[choice]
        )
        
        assert chunk.id == "chatcmpl-123"
        assert chunk.object == "chat.completion.chunk"  # Default value
        assert chunk.created == 1234567890
        assert chunk.model == "gpt-4"
        assert len(chunk.choices) == 1
        assert chunk.choices[0].delta == delta
        assert chunk.choices[0].finish_reason is None
    
    def test_final_stream_chunk(self):
        """Test creating a final streaming chunk."""
        delta = {}
        choice = MedusaStreamChoice(index=0, delta=delta, finish_reason="stop")
        
        chunk = MedusaStreamResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[choice]
        )
        
        assert chunk.choices[0].finish_reason == "stop"


class TestMedusaErrorResponse:
    """Test cases for MedusaErrorResponse schema."""
    
    def test_error_response(self):
        """Test creating an error response."""
        error_detail = {
            "message": "Invalid model specified",
            "type": "invalid_request_error",
            "param": "model",
            "code": "model_not_found"
        }
        
        error = MedusaErrorResponse(error=error_detail)
        
        assert error.error == error_detail
    
    def test_error_detail(self):
        """Test creating detailed error information."""
        detail = MedusaErrorDetail(
            message="Invalid model specified",
            type="invalid_request_error",
            param="model",
            code="model_not_found"
        )
        
        assert detail.message == "Invalid model specified"
        assert detail.type == "invalid_request_error"
        assert detail.param == "model"
        assert detail.code == "model_not_found"


class TestSerialization:
    """Test JSON serialization and deserialization."""
    
    def test_request_serialization(self):
        """Test that requests can be serialized to JSON."""
        messages = [
            MedusaMessage(role="user", content="Hello")
        ]
        
        request = MedusaChatRequest(
            model="gpt-4",
            messages=messages,
            temperature=0.7
        )
        
        # Test model_dump (Pydantic v2)
        data = request.model_dump(exclude_none=True)
        assert data["model"] == "gpt-4"
        assert len(data["messages"]) == 1
        assert data["temperature"] == 0.7
        assert "max_tokens" not in data  # Should be excluded since it's None
    
    def test_response_deserialization(self):
        """Test that responses can be created from JSON data."""
        data = {
            "id": "chatcmpl-123",
            "object": "chat.completion",
            "created": 1234567890,
            "model": "gpt-4",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "Hello!"
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 5,
                "total_tokens": 15
            }
        }
        
        response = MedusaChatResponse(**data)
        assert response.id == "chatcmpl-123"
        assert response.choices[0].message.content == "Hello!"
        assert response.usage.total_tokens == 15
