"""
Integration tests for the chat endpoint.

This module tests the complete /api/chat endpoint functionality including
request validation, translation, backend communication, and response formatting.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from datetime import datetime

from app.main import app
from app.services.backend_client_service import (
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
)
from app.api.schemas.medusa_schemas import (
    MedusaMessage,
    MedusaChatResponse,
    MedusaChoice,
    MedusaUsage,
)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_ollama_request():
    """Sample Ollama chat request."""
    return {
        "model": "llama3",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ]
    }


@pytest.fixture
def sample_medusa_response():
    """Sample Medusa response for mocking."""
    return MedusaChatResponse(
        id="chatcmpl-123",
        created=1234567890,
        model="gpt-4o-mini",
        choices=[
            MedusaChoice(
                index=0,
                message=MedusaMessage(role="assistant", content="I'm doing well, thank you!"),
                finish_reason="stop"
            )
        ],
        usage=MedusaUsage(
            prompt_tokens=10,
            completion_tokens=8,
            total_tokens=18
        )
    )


class TestChatEndpoint:
    """Test cases for the /api/chat endpoint."""
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_successful_chat_completion(self, mock_chat_completion, client, sample_ollama_request, sample_medusa_response):
        """Test successful chat completion request."""
        mock_chat_completion.return_value = sample_medusa_response
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify Ollama response format
        assert data["model"] == "llama3"  # Original model name
        assert data["message"]["role"] == "assistant"
        assert data["message"]["content"] == "I'm doing well, thank you!"
        assert data["done"] is True
        assert "created_at" in data
        
        # Verify usage information is translated
        assert data["prompt_eval_count"] == 10
        assert data["eval_count"] == 8
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_model_mapping(self, mock_chat_completion, client, sample_medusa_response):
        """Test that model mapping works correctly."""
        mock_chat_completion.return_value = sample_medusa_response
        
        request_data = {
            "model": "codellama",  # Should map to gpt-4
            "messages": [{"role": "user", "content": "Write a function"}]
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Response should use original model name
        assert data["model"] == "codellama"
        
        # Verify the backend was called with mapped model
        mock_chat_completion.assert_called_once()
        call_args = mock_chat_completion.call_args[0][0]  # First positional argument
        assert call_args.model == "gpt-4"  # Mapped model
    
    def test_invalid_request_no_messages(self, client):
        """Test request validation for missing messages."""
        request_data = {
            "model": "llama3",
            "messages": []
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 400
        assert "at least one message" in response.json()["detail"]
    
    def test_invalid_request_missing_model(self, client):
        """Test request validation for missing model."""
        request_data = {
            "messages": [{"role": "user", "content": "Hello"}]
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_invalid_request_invalid_role(self, client):
        """Test request validation for invalid message role."""
        request_data = {
            "model": "llama3",
            "messages": [{"role": "invalid", "content": "Hello"}]
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion_stream')
    def test_streaming_supported(self, mock_stream, client, sample_ollama_request):
        """Test that streaming requests are now supported."""
        from app.api.schemas.medusa_schemas import MedusaStreamResponse, MedusaStreamChoice

        # Mock streaming response
        async def mock_stream_generator():
            # First chunk
            yield MedusaStreamResponse(
                id="chatcmpl-123",
                created=1234567890,
                model="gpt-4o-mini",
                choices=[MedusaStreamChoice(
                    index=0,
                    delta={"role": "assistant", "content": "Hello"},
                    finish_reason=None
                )]
            )
            # Final chunk
            yield MedusaStreamResponse(
                id="chatcmpl-123",
                created=1234567890,
                model="gpt-4o-mini",
                choices=[MedusaStreamChoice(
                    index=0,
                    delta={"content": "!"},
                    finish_reason="stop"
                )]
            )

        mock_stream.return_value = mock_stream_generator()
        sample_ollama_request["stream"] = True

        response = client.post("/api/chat", json=sample_ollama_request)

        assert response.status_code == 200
        assert "application/x-ndjson" in response.headers["content-type"]

        # Check that we get streaming response
        content = response.content.decode()
        lines = [line for line in content.split('\n') if line.strip()]
        assert len(lines) >= 1  # Should have at least one chunk

    @patch('app.services.backend_client_service.BackendClientService.chat_completion_stream')
    def test_streaming_error_handling(self, mock_stream, client, sample_ollama_request):
        """Test error handling in streaming requests."""
        from app.services.backend_client_service import BackendConnectionError

        # Mock streaming error
        async def mock_stream_error():
            raise BackendConnectionError("Connection failed")
            yield  # This won't be reached

        mock_stream.return_value = mock_stream_error()
        sample_ollama_request["stream"] = True

        response = client.post("/api/chat", json=sample_ollama_request)

        assert response.status_code == 200  # Streaming starts successfully
        content = response.content.decode()

        # Should contain error information in the stream
        assert "error" in content or "Connection failed" in content

    @patch('app.services.backend_client_service.BackendClientService.chat_completion_stream')
    def test_streaming_translation(self, mock_stream, client, sample_ollama_request):
        """Test that streaming responses are properly translated to Ollama format."""
        from app.api.schemas.medusa_schemas import MedusaStreamResponse, MedusaStreamChoice
        import json

        # Mock streaming response with multiple chunks
        async def mock_stream_generator():
            yield MedusaStreamResponse(
                id="chatcmpl-123",
                created=1234567890,
                model="gpt-4o-mini",
                choices=[MedusaStreamChoice(
                    index=0,
                    delta={"role": "assistant", "content": "Hello"},
                    finish_reason=None
                )]
            )
            yield MedusaStreamResponse(
                id="chatcmpl-123",
                created=1234567890,
                model="gpt-4o-mini",
                choices=[MedusaStreamChoice(
                    index=0,
                    delta={"content": " world"},
                    finish_reason=None
                )]
            )
            yield MedusaStreamResponse(
                id="chatcmpl-123",
                created=1234567890,
                model="gpt-4o-mini",
                choices=[MedusaStreamChoice(
                    index=0,
                    delta={"content": "!"},
                    finish_reason="stop"
                )]
            )

        mock_stream.return_value = mock_stream_generator()
        sample_ollama_request["stream"] = True

        response = client.post("/api/chat", json=sample_ollama_request)

        assert response.status_code == 200
        content = response.content.decode()
        lines = [line for line in content.split('\n') if line.strip()]

        # Parse each chunk and verify Ollama format
        chunks = []
        for line in lines:
            try:
                chunk = json.loads(line)
                chunks.append(chunk)
            except json.JSONDecodeError:
                continue

        assert len(chunks) >= 3  # Should have at least 3 chunks

        # Verify Ollama format structure
        for chunk in chunks:
            assert "model" in chunk
            assert "created_at" in chunk
            assert "message" in chunk
            assert "done" in chunk
            assert chunk["model"] == "llama3"  # Original model name preserved

        # Last chunk should be marked as done
        assert chunks[-1]["done"] is True

    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_backend_timeout_error(self, mock_chat_completion, client, sample_ollama_request):
        """Test handling of backend timeout errors."""
        mock_chat_completion.side_effect = BackendTimeoutError("Request timed out")
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 504  # Gateway Timeout
        assert "Backend service timeout" in response.json()["detail"]
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_backend_connection_error(self, mock_chat_completion, client, sample_ollama_request):
        """Test handling of backend connection errors."""
        mock_chat_completion.side_effect = BackendConnectionError("Connection failed")
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 502  # Bad Gateway
        assert "Backend service unavailable" in response.json()["detail"]
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_backend_http_error_400(self, mock_chat_completion, client, sample_ollama_request):
        """Test handling of backend 400 errors."""
        mock_chat_completion.side_effect = BackendHTTPError("Invalid model", 400)
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 400
        assert "Backend error" in response.json()["detail"]
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_backend_http_error_500(self, mock_chat_completion, client, sample_ollama_request):
        """Test handling of backend 500 errors."""
        mock_chat_completion.side_effect = BackendHTTPError("Server error", 500)
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 502  # Bad Gateway
        assert "Backend service error" in response.json()["detail"]
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_translation_error(self, mock_chat_completion, client, sample_ollama_request):
        """Test handling of translation errors."""
        # Create a response with no choices to trigger translation error
        invalid_response = MedusaChatResponse(
            id="test",
            created=123,
            model="test",
            choices=[]
        )
        mock_chat_completion.return_value = invalid_response
        
        response = client.post("/api/chat", json=sample_ollama_request)
        
        assert response.status_code == 422
        assert "Request translation failed" in response.json()["detail"]
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_multimodal_request(self, mock_chat_completion, client, sample_medusa_response):
        """Test handling of multimodal requests with images."""
        mock_chat_completion.return_value = sample_medusa_response
        
        request_data = {
            "model": "llama3",
            "messages": [
                {
                    "role": "user",
                    "content": "What's in this image?",
                    "images": ["base64encodedimage"]
                }
            ]
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 200
        
        # Verify the backend was called with multimodal content
        mock_chat_completion.assert_called_once()
        call_args = mock_chat_completion.call_args[0][0]
        message_content = call_args.messages[0].content
        assert isinstance(message_content, list)
        assert len(message_content) == 2  # text + image
        assert message_content[0]["type"] == "text"
        assert message_content[1]["type"] == "image_url"
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_request_with_options(self, mock_chat_completion, client, sample_medusa_response):
        """Test handling of requests with Ollama options."""
        mock_chat_completion.return_value = sample_medusa_response
        
        request_data = {
            "model": "llama3",
            "messages": [{"role": "user", "content": "Hello"}],
            "options": {
                "temperature": 0.7,
                "max_tokens": 100,
                "top_p": 0.9
            }
        }
        
        response = client.post("/api/chat", json=request_data)
        
        assert response.status_code == 200
        
        # Verify options were translated to Medusa format
        mock_chat_completion.assert_called_once()
        call_args = mock_chat_completion.call_args[0][0]
        assert call_args.temperature == 0.7
        assert call_args.max_tokens == 100
        assert call_args.top_p == 0.9


class TestChatHealthEndpoint:
    """Test cases for the /api/chat/health endpoint."""
    
    @patch('app.services.backend_client_service.BackendClientService.health_check')
    def test_chat_health_success(self, mock_health_check, client):
        """Test successful chat health check."""
        mock_health_check.return_value = {"status": "healthy"}
        
        response = client.get("/api/chat/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "chat"
        assert data["backend_status"] == "connected"
        assert data["backend_health"] == {"status": "healthy"}
    
    @patch('app.services.backend_client_service.BackendClientService.health_check')
    def test_chat_health_backend_failure(self, mock_health_check, client):
        """Test chat health check with backend failure."""
        mock_health_check.side_effect = BackendConnectionError("Connection failed")
        
        response = client.get("/api/chat/health")
        
        assert response.status_code == 503  # Service Unavailable
        data = response.json()
        
        assert data["status"] == "unhealthy"
        assert data["service"] == "chat"
        assert data["backend_status"] == "disconnected"
        assert "Connection failed" in data["error"]


class TestEndToEndFlow:
    """End-to-end test cases for complete request flow."""
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_complete_flow_with_model_mapping(self, mock_chat_completion, client, sample_medusa_response):
        """Test complete flow from Ollama request to Ollama response with model mapping."""
        mock_chat_completion.return_value = sample_medusa_response
        
        # Send Ollama request
        ollama_request = {
            "model": "mistral",  # Will be mapped to gpt-3.5-turbo
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "options": {
                "temperature": 0.8,
                "max_tokens": 150
            }
        }
        
        response = client.post("/api/chat", json=ollama_request)
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Check Ollama response format
        assert data["model"] == "mistral"  # Original model
        assert data["message"]["role"] == "assistant"
        assert data["message"]["content"] == "I'm doing well, thank you!"
        assert data["done"] is True
        
        # Verify backend was called correctly
        mock_chat_completion.assert_called_once()
        backend_request = mock_chat_completion.call_args[0][0]
        
        # Check model mapping
        assert backend_request.model == "gpt-3.5-turbo"
        
        # Check message translation
        assert len(backend_request.messages) == 2
        assert backend_request.messages[0].role == "system"
        assert backend_request.messages[1].role == "user"
        
        # Check options translation
        assert backend_request.temperature == 0.8
        assert backend_request.max_tokens == 150
    
    @patch('app.services.backend_client_service.BackendClientService.chat_completion')
    def test_error_propagation(self, mock_chat_completion, client):
        """Test that errors are properly propagated through the stack."""
        # Test various error scenarios
        error_scenarios = [
            (BackendTimeoutError("Timeout"), 504),
            (BackendConnectionError("Connection failed"), 502),
            (BackendHTTPError("Bad request", 400), 400),
            (BackendHTTPError("Server error", 500), 502),
        ]
        
        for error, expected_status in error_scenarios:
            mock_chat_completion.side_effect = error
            
            response = client.post("/api/chat", json={
                "model": "llama3",
                "messages": [{"role": "user", "content": "Hello"}]
            })
            
            assert response.status_code == expected_status
            assert "detail" in response.json()


class TestRequestValidation:
    """Test cases for request validation."""
    
    def test_valid_request_formats(self, client):
        """Test various valid request formats."""
        valid_requests = [
            # Minimal request
            {
                "model": "llama3",
                "messages": [{"role": "user", "content": "Hello"}]
            },
            # Request with system message
            {
                "model": "llama3",
                "messages": [
                    {"role": "system", "content": "You are helpful"},
                    {"role": "user", "content": "Hello"}
                ]
            },
            # Request with options
            {
                "model": "llama3",
                "messages": [{"role": "user", "content": "Hello"}],
                "options": {"temperature": 0.5}
            },
            # Request with all optional fields
            {
                "model": "llama3",
                "messages": [{"role": "user", "content": "Hello"}],
                "stream": False,
                "format": "json",
                "options": {"temperature": 0.7},
                "template": "custom",
                "system": "system message",
                "raw": False,
                "keep_alive": "5m"
            }
        ]
        
        for request_data in valid_requests:
            with patch('app.services.backend_client_service.BackendClientService.chat_completion') as mock:
                mock.return_value = MedusaChatResponse(
                    id="test", created=123, model="test",
                    choices=[MedusaChoice(index=0, message=MedusaMessage(role="assistant", content="test"))]
                )
                
                response = client.post("/api/chat", json=request_data)
                assert response.status_code == 200, f"Failed for request: {request_data}"
