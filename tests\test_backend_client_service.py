"""
Tests for backend client service.

This module tests the HTTP client for communicating with the MedusaXD backend API.
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock
import httpx

from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)
from app.api.schemas.medusa_schemas import (
    MedusaMessage,
    MedusaChatRequest,
    MedusaChatResponse,
    MedusaChoice,
    MedusaUsage,
    MedusaStreamResponse,
    MedusaStreamChoice,
)
from app.core.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.medusa_backend_url = "https://api.example.com"
    settings.api_timeout = 30
    settings.max_concurrent_requests = 100
    settings.app_name = "Test Bridge"
    return settings


@pytest.fixture
def backend_client(mock_settings):
    """Create backend client for testing."""
    return BackendClientService(mock_settings)


@pytest.fixture
def sample_medusa_request():
    """Create a sample Medusa chat request."""
    return MedusaChatRequest(
        model="gpt-4o-mini",
        messages=[
            MedusaMessage(role="user", content="Hello, how are you?")
        ]
    )


@pytest.fixture
def sample_medusa_response():
    """Create a sample Medusa chat response."""
    return MedusaChatResponse(
        id="chatcmpl-123",
        created=1234567890,
        model="gpt-4o-mini",
        choices=[
            MedusaChoice(
                index=0,
                message=MedusaMessage(role="assistant", content="I'm doing well, thank you!"),
                finish_reason="stop"
            )
        ],
        usage=MedusaUsage(
            prompt_tokens=10,
            completion_tokens=8,
            total_tokens=18
        )
    )


class TestBackendClientService:
    """Test cases for BackendClientService."""
    
    def test_initialization(self, mock_settings):
        """Test that the client initializes correctly."""
        client = BackendClientService(mock_settings)
        
        assert client.base_url == "https://api.example.com"
        assert client.timeout == 30
        assert client.settings == mock_settings
        assert client.client is not None
    
    def test_initialization_strips_trailing_slash(self):
        """Test that trailing slash is stripped from base URL."""
        settings = Mock(spec=Settings)
        settings.medusa_backend_url = "https://api.example.com/"
        settings.api_timeout = 30
        settings.max_concurrent_requests = 100
        settings.app_name = "Test"
        
        client = BackendClientService(settings)
        
        assert client.base_url == "https://api.example.com"
    
    @pytest.mark.asyncio
    async def test_chat_completion_success(self, backend_client, sample_medusa_request, sample_medusa_response):
        """Test successful chat completion request."""
        # Mock the HTTP client
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_medusa_response.model_dump()
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        result = await backend_client.chat_completion(sample_medusa_request)
        
        assert isinstance(result, MedusaChatResponse)
        assert result.id == "chatcmpl-123"
        assert result.choices[0].message.content == "I'm doing well, thank you!"
        
        # Verify the request was made correctly
        backend_client.client.post.assert_called_once_with(
            "/v1/chat/completions",
            json=sample_medusa_request.model_dump(exclude_none=True)
        )
    
    @pytest.mark.asyncio
    async def test_chat_completion_timeout(self, backend_client, sample_medusa_request):
        """Test chat completion request timeout."""
        backend_client.client.post = AsyncMock(side_effect=httpx.TimeoutException("Timeout"))
        
        with pytest.raises(BackendTimeoutError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert "timed out after 30 seconds" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_chat_completion_connection_error(self, backend_client, sample_medusa_request):
        """Test chat completion connection error."""
        backend_client.client.post = AsyncMock(side_effect=httpx.ConnectError("Connection failed"))
        
        with pytest.raises(BackendConnectionError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert "Failed to connect to backend" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_chat_completion_http_error_400(self, backend_client, sample_medusa_request):
        """Test chat completion with 400 error from backend."""
        error_response = {
            "error": {
                "message": "Invalid model specified",
                "type": "invalid_request_error",
                "param": "model"
            }
        }
        
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = error_response
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert exc_info.value.status_code == 400
        assert "Invalid model specified" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_chat_completion_http_error_500(self, backend_client, sample_medusa_request):
        """Test chat completion with 500 error from backend."""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"error": {"message": "Internal server error"}}
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert exc_info.value.status_code == 500
    
    @pytest.mark.asyncio
    async def test_chat_completion_invalid_json_response(self, backend_client, sample_medusa_request):
        """Test chat completion with invalid JSON response."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_response.text = "Invalid response"
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert "Backend returned invalid JSON response" in str(exc_info.value)
        assert exc_info.value.status_code == 200
    
    @pytest.mark.asyncio
    async def test_chat_completion_invalid_response_format(self, backend_client, sample_medusa_request):
        """Test chat completion with response that doesn't match schema."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"invalid": "response"}
        mock_response.text = '{"invalid": "response"}'
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client.chat_completion(sample_medusa_request)
        
        assert "Failed to parse backend response" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, backend_client):
        """Test successful health check."""
        health_data = {"status": "healthy", "version": "1.0.0"}
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = health_data
        
        backend_client.client.get = AsyncMock(return_value=mock_response)
        
        result = await backend_client.health_check()
        
        assert result == health_data
        backend_client.client.get.assert_called_once_with("/health")
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, backend_client):
        """Test health check failure."""
        mock_response = Mock()
        mock_response.status_code = 503
        
        backend_client.client.get = AsyncMock(return_value=mock_response)
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client.health_check()
        
        assert exc_info.value.status_code == 503
        assert "Backend health check failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_health_check_connection_error(self, backend_client):
        """Test health check connection error."""
        backend_client.client.get = AsyncMock(side_effect=httpx.ConnectError("Connection failed"))
        
        with pytest.raises(BackendConnectionError) as exc_info:
            await backend_client.health_check()
        
        assert "Health check failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_close(self, backend_client):
        """Test closing the client."""
        backend_client.client.aclose = AsyncMock()
        
        await backend_client.close()
        
        backend_client.client.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_settings):
        """Test using the client as an async context manager."""
        async with BackendClientService(mock_settings) as client:
            assert client is not None
            client.client.aclose = AsyncMock()
        
        # aclose should be called when exiting context
        client.client.aclose.assert_called_once()


class TestErrorHandling:
    """Test cases for error handling in backend client."""
    
    @pytest.mark.asyncio
    async def test_handle_success_response(self, backend_client, sample_medusa_response):
        """Test handling successful response."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_medusa_response.model_dump()
        
        result = await backend_client._handle_success_response(mock_response)
        
        assert isinstance(result, MedusaChatResponse)
        assert result.id == sample_medusa_response.id
    
    @pytest.mark.asyncio
    async def test_handle_error_response_with_json(self, backend_client):
        """Test handling error response with JSON error details."""
        error_data = {
            "error": {
                "message": "Model not found",
                "type": "invalid_request_error"
            }
        }
        
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.json.return_value = error_data
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client._handle_error_response(mock_response)
        
        assert exc_info.value.status_code == 404
        assert "Model not found" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_handle_error_response_without_json(self, backend_client):
        """Test handling error response without JSON."""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_response.text = "Internal Server Error"
        
        with pytest.raises(BackendHTTPError) as exc_info:
            await backend_client._handle_error_response(mock_response)
        
        assert exc_info.value.status_code == 500
        assert "Backend returned 500" in str(exc_info.value)


class TestFactoryFunction:
    """Test cases for the factory function."""
    
    def test_get_backend_client(self, mock_settings):
        """Test the factory function creates a client correctly."""
        client = get_backend_client(mock_settings)
        
        assert isinstance(client, BackendClientService)
        assert client.settings == mock_settings


class TestRequestProcessing:
    """Test cases for request processing."""
    
    @pytest.mark.asyncio
    async def test_request_data_serialization(self, backend_client, sample_medusa_request):
        """Test that request data is properly serialized."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "test",
            "created": 123,
            "model": "test",
            "choices": [{"index": 0, "message": {"role": "assistant", "content": "test"}}]
        }
        
        backend_client.client.post = AsyncMock(return_value=mock_response)
        
        await backend_client.chat_completion(sample_medusa_request)
        
        # Verify the request was serialized with exclude_none=True
        call_args = backend_client.client.post.call_args
        assert call_args[1]["json"] == sample_medusa_request.model_dump(exclude_none=True)


class TestClientConfiguration:
    """Test cases for client configuration."""
    
    def test_client_headers(self, backend_client):
        """Test that client is configured with correct headers."""
        headers = backend_client.client.headers
        
        assert headers["Content-Type"] == "application/json"
        assert "Test Bridge" in headers["User-Agent"]
    
    def test_client_timeout(self, backend_client):
        """Test that client timeout is configured correctly."""
        assert backend_client.client.timeout.read == 30
    
    def test_client_limits(self, backend_client):
        """Test that client connection limits are configured."""
        # HTTPX client limits are set during initialization
        # We can verify they were set by checking the client was created successfully
        assert backend_client.client is not None
        assert backend_client.settings.max_concurrent_requests == 100
