["tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_connection_error", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_http_error_400", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_http_error_500", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_invalid_json_response", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_invalid_response_format", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_success", "tests/test_backend_client_service.py::TestBackendClientService::test_chat_completion_timeout", "tests/test_backend_client_service.py::TestBackendClientService::test_close", "tests/test_backend_client_service.py::TestBackendClientService::test_context_manager", "tests/test_backend_client_service.py::TestBackendClientService::test_health_check_connection_error", "tests/test_backend_client_service.py::TestBackendClientService::test_health_check_failure", "tests/test_backend_client_service.py::TestBackendClientService::test_health_check_success", "tests/test_backend_client_service.py::TestBackendClientService::test_initialization", "tests/test_backend_client_service.py::TestBackendClientService::test_initialization_strips_trailing_slash", "tests/test_backend_client_service.py::TestClientConfiguration::test_client_headers", "tests/test_backend_client_service.py::TestClientConfiguration::test_client_limits", "tests/test_backend_client_service.py::TestClientConfiguration::test_client_timeout", "tests/test_backend_client_service.py::TestErrorHandling::test_handle_error_response_with_json", "tests/test_backend_client_service.py::TestErrorHandling::test_handle_error_response_without_json", "tests/test_backend_client_service.py::TestErrorHandling::test_handle_success_response", "tests/test_backend_client_service.py::TestFactoryFunction::test_get_backend_client", "tests/test_backend_client_service.py::TestRequestProcessing::test_request_data_serialization", "tests/test_chat_endpoint.py::TestChatEndpoint::test_backend_connection_error", "tests/test_chat_endpoint.py::TestChatEndpoint::test_backend_http_error_400", "tests/test_chat_endpoint.py::TestChatEndpoint::test_backend_http_error_500", "tests/test_chat_endpoint.py::TestChatEndpoint::test_backend_timeout_error", "tests/test_chat_endpoint.py::TestChatEndpoint::test_invalid_request_invalid_role", "tests/test_chat_endpoint.py::TestChatEndpoint::test_invalid_request_missing_model", "tests/test_chat_endpoint.py::TestChatEndpoint::test_invalid_request_no_messages", "tests/test_chat_endpoint.py::TestChatEndpoint::test_model_mapping", "tests/test_chat_endpoint.py::TestChatEndpoint::test_multimodal_request", "tests/test_chat_endpoint.py::TestChatEndpoint::test_request_with_options", "tests/test_chat_endpoint.py::TestChatEndpoint::test_streaming_not_supported", "tests/test_chat_endpoint.py::TestChatEndpoint::test_successful_chat_completion", "tests/test_chat_endpoint.py::TestChatEndpoint::test_translation_error", "tests/test_chat_endpoint.py::TestChatHealthEndpoint::test_chat_health_backend_failure", "tests/test_chat_endpoint.py::TestChatHealthEndpoint::test_chat_health_success", "tests/test_chat_endpoint.py::TestEndToEndFlow::test_complete_flow_with_model_mapping", "tests/test_chat_endpoint.py::TestEndToEndFlow::test_error_propagation", "tests/test_chat_endpoint.py::TestRequestValidation::test_valid_request_formats", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[0-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[1-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[FALSE-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[False-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[TRUE-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[True-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[false-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[true-True]", "tests/test_config.py::TestDependencyInjection::test_configuration_changes_reflected", "tests/test_config.py::TestDependencyInjection::test_settings_dependency_injection", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_invalid_json", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_invalid_log_level", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_missing_required_field", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_out_of_range_values", "tests/test_config.py::TestGetSettings::test_get_settings_cache_clear", "tests/test_config.py::TestGetSettings::test_get_settings_caching", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[critical]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[debug]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[error]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[info]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[warning]", "tests/test_config.py::TestLogLevelValidation::test_invalid_log_level", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[CRITICAL]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[DEBUG]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[ERROR]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[INFO]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[WARNING]", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_dict_input", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_empty_string", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_invalid_json", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_json_string", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_non_dict_json", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_type_conversion", "tests/test_config.py::TestSettings::test_api_timeout_validation", "tests/test_config.py::TestSettings::test_max_concurrent_requests_validation", "tests/test_config.py::TestSettings::test_missing_required_field", "tests/test_config.py::TestSettings::test_settings_with_full_config", "tests/test_config.py::TestSettings::test_settings_with_minimal_config", "tests/test_config.py::TestSettingsValidation::test_api_timeout_validation", "tests/test_config.py::TestSettingsValidation::test_max_concurrent_requests_validation", "tests/test_config.py::TestSettingsValidation::test_missing_required_field", "tests/test_config.py::TestSettingsValidation::test_settings_with_full_config", "tests/test_config.py::TestSettingsValidation::test_settings_with_minimal_config", "tests/test_health.py::test_health_check_content_type", "tests/test_health.py::test_health_check_response_structure", "tests/test_health.py::test_health_check_success", "tests/test_medusa_schemas.py::TestMedusaChatRequest::test_full_request", "tests/test_medusa_schemas.py::TestMedusaChatRequest::test_max_tokens_validation", "tests/test_medusa_schemas.py::TestMedusaChatRequest::test_minimal_request", "tests/test_medusa_schemas.py::TestMedusaChatRequest::test_temperature_validation", "tests/test_medusa_schemas.py::TestMedusaChatResponse::test_full_response", "tests/test_medusa_schemas.py::TestMedusaChatResponse::test_minimal_response", "tests/test_medusa_schemas.py::TestMedusaChoice::test_choice_creation", "tests/test_medusa_schemas.py::TestMedusaChoice::test_choice_with_logprobs", "tests/test_medusa_schemas.py::TestMedusaErrorResponse::test_error_detail", "tests/test_medusa_schemas.py::TestMedusaErrorResponse::test_error_response", "tests/test_medusa_schemas.py::TestMedusaMessage::test_assistant_with_tool_calls", "tests/test_medusa_schemas.py::TestMedusaMessage::test_invalid_role", "tests/test_medusa_schemas.py::TestMedusaMessage::test_multimodal_message", "tests/test_medusa_schemas.py::TestMedusaMessage::test_simple_message", "tests/test_medusa_schemas.py::TestMedusaStreamResponse::test_final_stream_chunk", "tests/test_medusa_schemas.py::TestMedusaStreamResponse::test_stream_chunk", "tests/test_medusa_schemas.py::TestMedusaUsage::test_missing_fields", "tests/test_medusa_schemas.py::TestMedusaUsage::test_usage_creation", "tests/test_medusa_schemas.py::TestSerialization::test_request_serialization", "tests/test_medusa_schemas.py::TestSerialization::test_response_deserialization", "tests/test_ollama_schemas.py::TestOllamaChatRequest::test_empty_messages", "tests/test_ollama_schemas.py::TestOllamaChatRequest::test_full_request", "tests/test_ollama_schemas.py::TestOllamaChatRequest::test_minimal_request", "tests/test_ollama_schemas.py::TestOllamaChatRequest::test_missing_model", "tests/test_ollama_schemas.py::TestOllamaChatResponse::test_full_response", "tests/test_ollama_schemas.py::TestOllamaChatResponse::test_minimal_response", "tests/test_ollama_schemas.py::TestOllamaErrorResponse::test_error_response", "tests/test_ollama_schemas.py::TestOllamaErrorResponse::test_missing_error", "tests/test_ollama_schemas.py::TestOllamaMessage::test_invalid_role", "tests/test_ollama_schemas.py::TestOllamaMessage::test_message_with_images", "tests/test_ollama_schemas.py::TestOllamaMessage::test_missing_content", "tests/test_ollama_schemas.py::TestOllamaMessage::test_valid_message", "tests/test_ollama_schemas.py::TestOllamaResponseMessage::test_default_role", "tests/test_ollama_schemas.py::TestOllamaResponseMessage::test_valid_response_message", "tests/test_ollama_schemas.py::TestOllamaStreamResponse::test_final_stream_chunk", "tests/test_ollama_schemas.py::TestOllamaStreamResponse::test_stream_chunk", "tests/test_ollama_schemas.py::TestSerialization::test_request_serialization", "tests/test_ollama_schemas.py::TestSerialization::test_response_deserialization", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_request_unmapped_model", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_request_with_images", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_request_with_model_mapping", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_request_with_options", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_request_with_streaming", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_response_empty_content", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_response_no_choices", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_response_simple", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_response_with_usage", "tests/test_translation_service.py::TestChatTranslationStrategy::test_translate_simple_request", "tests/test_translation_service.py::TestMessageTranslation::test_translate_message_with_images", "tests/test_translation_service.py::TestMessageTranslation::test_translate_text_message", "tests/test_translation_service.py::TestModelMapping::test_empty_model_mappings", "tests/test_translation_service.py::TestModelMapping::test_model_mapping_function", "tests/test_translation_service.py::TestOptionsMapping::test_apply_empty_options", "tests/test_translation_service.py::TestOptionsMapping::test_apply_ollama_options", "tests/test_translation_service.py::TestTranslationService::test_translate_chat_request", "tests/test_translation_service.py::TestTranslationService::test_translate_chat_response"]