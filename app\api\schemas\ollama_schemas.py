"""
Ollama API format schemas.

This module contains Pydantic models that represent the Ollama API format
for chat requests and responses, following the official Ollama API specification.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Literal

from pydantic import BaseModel, Field


class OllamaMessage(BaseModel):
    """
    Represents a message in Ollama chat format.
    
    Supports system, user, and assistant roles with optional image content.
    """
    
    role: Literal["system", "user", "assistant"] = Field(
        ...,
        description="The role of the message sender"
    )
    
    content: str = Field(
        ...,
        description="The text content of the message"
    )
    
    images: Optional[List[str]] = Field(
        None,
        description="Optional list of base64-encoded images"
    )


class OllamaChatRequest(BaseModel):
    """
    Represents an Ollama chat completion request.
    
    This matches the format expected by Ollama's /api/chat endpoint.
    """
    
    model: str = Field(
        ...,
        description="The model name to use for the chat completion"
    )
    
    messages: List[OllamaMessage] = Field(
        ...,
        description="List of messages in the conversation"
    )
    
    stream: Optional[bool] = Field(
        False,
        description="Whether to stream the response"
    )
    
    format: Optional[str] = Field(
        None,
        description="Response format (e.g., 'json')"
    )
    
    options: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional model options"
    )
    
    template: Optional[str] = Field(
        None,
        description="Custom prompt template"
    )
    
    system: Optional[str] = Field(
        None,
        description="System message to use"
    )
    
    raw: Optional[bool] = Field(
        None,
        description="Whether to use raw mode"
    )
    
    keep_alive: Optional[str] = Field(
        None,
        description="How long to keep the model loaded"
    )


class OllamaResponseMessage(BaseModel):
    """
    Represents a message in an Ollama response.
    """
    
    role: Literal["assistant"] = Field(
        "assistant",
        description="The role of the response message"
    )
    
    content: str = Field(
        ...,
        description="The generated response content"
    )


class OllamaChatResponse(BaseModel):
    """
    Represents an Ollama chat completion response.
    
    This matches the format returned by Ollama's /api/chat endpoint
    for non-streaming responses.
    """
    
    model: str = Field(
        ...,
        description="The model that generated the response"
    )
    
    created_at: datetime = Field(
        ...,
        description="Timestamp when the response was created"
    )
    
    message: OllamaResponseMessage = Field(
        ...,
        description="The generated message"
    )
    
    done: bool = Field(
        True,
        description="Whether the response is complete"
    )
    
    total_duration: Optional[int] = Field(
        None,
        description="Total time taken in nanoseconds"
    )
    
    load_duration: Optional[int] = Field(
        None,
        description="Time taken to load the model in nanoseconds"
    )
    
    prompt_eval_count: Optional[int] = Field(
        None,
        description="Number of tokens in the prompt"
    )
    
    prompt_eval_duration: Optional[int] = Field(
        None,
        description="Time taken to evaluate the prompt in nanoseconds"
    )
    
    eval_count: Optional[int] = Field(
        None,
        description="Number of tokens in the response"
    )
    
    eval_duration: Optional[int] = Field(
        None,
        description="Time taken to generate the response in nanoseconds"
    )


class OllamaStreamResponse(BaseModel):
    """
    Represents a single chunk in an Ollama streaming response.
    
    Used for streaming chat completions.
    """
    
    model: str = Field(
        ...,
        description="The model generating the response"
    )
    
    created_at: datetime = Field(
        ...,
        description="Timestamp when the chunk was created"
    )
    
    message: OllamaResponseMessage = Field(
        ...,
        description="The message chunk"
    )
    
    done: bool = Field(
        ...,
        description="Whether this is the final chunk"
    )
    
    # Metadata fields (only present in final chunk when done=true)
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None


class OllamaErrorResponse(BaseModel):
    """
    Represents an error response from Ollama.
    """
    
    error: str = Field(
        ...,
        description="Error message"
    )
