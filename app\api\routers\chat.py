"""
Chat endpoint router for Ollama-compatible API.

This module provides the /api/chat endpoint that accepts Ollama-formatted
requests and translates them to/from the backend API format.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import J<PERSON>NResponse

from app.api.schemas.ollama_schemas import OllamaChatRequest, OllamaChatResponse
from app.core.config import Settings, get_settings
from app.services.translation_service import TranslationService
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.
    
    Args:
        settings: Application settings
        
    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.
    
    Args:
        settings: Application settings
        
    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.post(
    "/api/chat",
    response_model=OllamaChatResponse,
    summary="Chat Completion",
    description="Send a chat completion request in Ollama format and receive a response",
    responses={
        200: {"description": "Successful chat completion"},
        400: {"description": "Invalid request format or parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def chat_completion(
    request: OllamaChatRequest,
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaChatResponse:
    """
    Handle chat completion requests in Ollama format.
    
    This endpoint accepts Ollama-formatted chat requests, translates them to
    OpenAI format for the backend, and translates the response back to Ollama format.
    
    Args:
        request: The Ollama chat completion request
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaChatResponse: The chat completion response in Ollama format
        
    Raises:
        HTTPException: For various error conditions
    """
    logger.info(f"Chat completion request for model: {request.model}")
    logger.debug(f"Request details: {len(request.messages)} messages, stream={request.stream}")
    
    # Validate request
    if not request.messages:
        logger.warning("Chat request with no messages")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Request must contain at least one message"
        )
    
    # Check for streaming (not supported in this story)
    if request.stream:
        logger.warning("Streaming request received but not supported in this implementation")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Streaming is not yet supported. Please set stream=false or omit the stream parameter."
        )
    
    try:
        # Step 1: Translate Ollama request to Medusa format
        logger.debug("Translating Ollama request to Medusa format")
        medusa_request = translation_service.translate_chat_request(request)
        logger.debug(f"Translated to model: {medusa_request.model}")
        
        # Step 2: Send request to backend
        logger.debug("Sending request to backend")
        medusa_response = await backend_client.chat_completion(medusa_request)
        logger.debug(f"Received response with {len(medusa_response.choices)} choices")
        
        # Step 3: Translate Medusa response back to Ollama format
        logger.debug("Translating Medusa response to Ollama format")
        ollama_response = translation_service.translate_chat_response(
            medusa_response, 
            request.model  # Use original model name
        )
        
        logger.info(f"Chat completion successful for model: {request.model}")
        return ollama_response
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout: {e}")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail=f"Backend service timeout: {str(e)}"
        )
    
    except BackendConnectionError as e:
        logger.error(f"Backend connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Backend service unavailable: {str(e)}"
        )
    
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error: {e}")
        
        # Map backend HTTP errors to appropriate client errors
        if 400 <= e.status_code < 500:
            # Client errors from backend - pass through with translation
            detail = f"Backend error: {str(e)}"
            if e.status_code == 400:
                status_code = status.HTTP_400_BAD_REQUEST
            elif e.status_code == 401:
                status_code = status.HTTP_401_UNAUTHORIZED
            elif e.status_code == 403:
                status_code = status.HTTP_403_FORBIDDEN
            elif e.status_code == 404:
                status_code = status.HTTP_404_NOT_FOUND
            elif e.status_code == 422:
                status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
            else:
                status_code = status.HTTP_400_BAD_REQUEST
        else:
            # Server errors from backend
            status_code = status.HTTP_502_BAD_GATEWAY
            detail = f"Backend service error: {str(e)}"
        
        raise HTTPException(status_code=status_code, detail=detail)
    
    except ValueError as e:
        logger.error(f"Translation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Request translation failed: {str(e)}"
        )
    
    except Exception as e:
        logger.error(f"Unexpected error in chat completion: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred"
        )


@router.get(
    "/api/chat/health",
    summary="Chat Service Health Check",
    description="Check the health of the chat service and backend connectivity"
)
async def chat_health_check(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> Dict[str, Any]:
    """
    Health check endpoint for the chat service.
    
    Verifies that the backend service is reachable and responsive.
    
    Args:
        backend_client: Client for backend API communication
        
    Returns:
        Dict[str, Any]: Health status information
        
    Raises:
        HTTPException: If backend is not healthy
    """
    logger.debug("Chat service health check requested")
    
    try:
        # Check backend connectivity
        backend_health = await backend_client.health_check()
        
        return {
            "status": "healthy",
            "service": "chat",
            "backend_status": "connected",
            "backend_health": backend_health
        }
        
    except BackendClientError as e:
        logger.warning(f"Backend health check failed: {e}")
        
        # Return unhealthy status but don't raise exception
        # This allows monitoring to detect issues without breaking the service
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "service": "chat", 
                "backend_status": "disconnected",
                "error": str(e)
            }
        )
    
    except Exception as e:
        logger.error(f"Unexpected error in chat health check: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Health check failed"
        )
