"""
Tests for translation service.

This module tests the translation logic between Ollama and OpenAI API formats.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from app.services.translation_service import (
    TranslationService,
    ChatTranslationStrategy,
)
from app.api.schemas.ollama_schemas import (
    OllamaMessage,
    OllamaChatRequest,
    OllamaChatResponse,
    OllamaResponseMessage,
)
from app.api.schemas.medusa_schemas import (
    MedusaMessage,
    MedusaChatRequest,
    MedusaChatResponse,
    MedusaChoice,
    MedusaUsage,
)
from app.core.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.model_mappings = {
        "llama3": "gpt-4o-mini",
        "codellama": "gpt-4",
        "mistral": "gpt-3.5-turbo"
    }
    return settings


@pytest.fixture
def translation_service(mock_settings):
    """Create translation service for testing."""
    return TranslationService(mock_settings)


@pytest.fixture
def chat_strategy(mock_settings):
    """Create chat translation strategy for testing."""
    return ChatTranslationStrategy(mock_settings)


class TestChatTranslationStrategy:
    """Test cases for ChatTranslationStrategy."""
    
    def test_translate_simple_request(self, chat_strategy):
        """Test translating a simple Ollama request to Medusa format."""
        ollama_request = OllamaChatRequest(
            model="llama3",
            messages=[
                OllamaMessage(role="user", content="Hello, how are you?")
            ]
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert medusa_request.model == "gpt-4o-mini"  # Mapped model
        assert len(medusa_request.messages) == 1
        assert medusa_request.messages[0].role == "user"
        assert medusa_request.messages[0].content == "Hello, how are you?"
        assert medusa_request.stream is False
    
    def test_translate_request_with_model_mapping(self, chat_strategy):
        """Test that model mapping works correctly."""
        ollama_request = OllamaChatRequest(
            model="codellama",
            messages=[OllamaMessage(role="user", content="Write a function")]
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert medusa_request.model == "gpt-4"  # Mapped from codellama
    
    def test_translate_request_unmapped_model(self, chat_strategy):
        """Test that unmapped models pass through unchanged."""
        ollama_request = OllamaChatRequest(
            model="unknown-model",
            messages=[OllamaMessage(role="user", content="Hello")]
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert medusa_request.model == "unknown-model"  # No mapping, pass through
    
    def test_translate_request_with_images(self, chat_strategy):
        """Test translating a request with images to multimodal format."""
        ollama_request = OllamaChatRequest(
            model="llama3",
            messages=[
                OllamaMessage(
                    role="user",
                    content="What's in this image?",
                    images=["base64encodedimage"]
                )
            ]
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert len(medusa_request.messages) == 1
        message = medusa_request.messages[0]
        assert isinstance(message.content, list)
        assert len(message.content) == 2
        assert message.content[0]["type"] == "text"
        assert message.content[0]["text"] == "What's in this image?"
        assert message.content[1]["type"] == "image_url"
        assert "base64encodedimage" in message.content[1]["image_url"]["url"]
    
    def test_translate_request_with_options(self, chat_strategy):
        """Test translating a request with Ollama options."""
        ollama_request = OllamaChatRequest(
            model="llama3",
            messages=[OllamaMessage(role="user", content="Hello")],
            options={
                "temperature": 0.7,
                "max_tokens": 100,
                "top_p": 0.9,
                "num_predict": 150,  # Should map to max_tokens
                "stop": ["END"]
            }
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert medusa_request.temperature == 0.7
        assert medusa_request.max_tokens == 150  # num_predict takes precedence
        assert medusa_request.top_p == 0.9
        assert medusa_request.stop == ["END"]
    
    def test_translate_request_with_streaming(self, chat_strategy):
        """Test translating a streaming request."""
        ollama_request = OllamaChatRequest(
            model="llama3",
            messages=[OllamaMessage(role="user", content="Hello")],
            stream=True
        )
        
        medusa_request = chat_strategy.translate_request(ollama_request)
        
        assert medusa_request.stream is True
    
    def test_translate_response_simple(self, chat_strategy):
        """Test translating a simple Medusa response to Ollama format."""
        medusa_response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4o-mini",
            choices=[
                MedusaChoice(
                    index=0,
                    message=MedusaMessage(role="assistant", content="Hello! I'm doing well."),
                    finish_reason="stop"
                )
            ]
        )
        
        ollama_response = chat_strategy.translate_response(medusa_response, "llama3")
        
        assert ollama_response.model == "llama3"  # Original model name
        assert ollama_response.message.role == "assistant"
        assert ollama_response.message.content == "Hello! I'm doing well."
        assert ollama_response.done is True
        assert isinstance(ollama_response.created_at, datetime)
    
    def test_translate_response_with_usage(self, chat_strategy):
        """Test translating a response with usage information."""
        medusa_response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[
                MedusaChoice(
                    index=0,
                    message=MedusaMessage(role="assistant", content="Hello!"),
                    finish_reason="stop"
                )
            ],
            usage=MedusaUsage(
                prompt_tokens=10,
                completion_tokens=5,
                total_tokens=15
            )
        )
        
        ollama_response = chat_strategy.translate_response(medusa_response, "codellama")
        
        assert ollama_response.prompt_eval_count == 10
        assert ollama_response.eval_count == 5
    
    def test_translate_response_no_choices(self, chat_strategy):
        """Test that response with no choices raises an error."""
        medusa_response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[]
        )
        
        with pytest.raises(ValueError, match="Medusa response contains no choices"):
            chat_strategy.translate_response(medusa_response, "llama3")
    
    def test_translate_response_empty_content(self, chat_strategy):
        """Test translating a response with empty content."""
        medusa_response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-4",
            choices=[
                MedusaChoice(
                    index=0,
                    message=MedusaMessage(role="assistant", content=None),
                    finish_reason="stop"
                )
            ]
        )
        
        ollama_response = chat_strategy.translate_response(medusa_response, "llama3")
        
        assert ollama_response.message.content == ""  # None becomes empty string


class TestTranslationService:
    """Test cases for TranslationService."""
    
    def test_translate_chat_request(self, translation_service):
        """Test the main chat request translation method."""
        ollama_request = OllamaChatRequest(
            model="mistral",
            messages=[
                OllamaMessage(role="system", content="You are helpful"),
                OllamaMessage(role="user", content="Hello")
            ]
        )
        
        medusa_request = translation_service.translate_chat_request(ollama_request)
        
        assert medusa_request.model == "gpt-3.5-turbo"  # Mapped model
        assert len(medusa_request.messages) == 2
        assert medusa_request.messages[0].role == "system"
        assert medusa_request.messages[1].role == "user"
    
    def test_translate_chat_response(self, translation_service):
        """Test the main chat response translation method."""
        medusa_response = MedusaChatResponse(
            id="chatcmpl-123",
            created=1234567890,
            model="gpt-3.5-turbo",
            choices=[
                MedusaChoice(
                    index=0,
                    message=MedusaMessage(role="assistant", content="Hi there!"),
                    finish_reason="stop"
                )
            ]
        )
        
        ollama_response = translation_service.translate_chat_response(
            medusa_response, 
            "mistral"
        )
        
        assert ollama_response.model == "mistral"  # Original model
        assert ollama_response.message.content == "Hi there!"
        assert ollama_response.done is True


class TestModelMapping:
    """Test cases for model name mapping functionality."""
    
    def test_model_mapping_function(self, chat_strategy):
        """Test the internal model mapping function."""
        # Test mapped models
        assert chat_strategy._map_model_name("llama3") == "gpt-4o-mini"
        assert chat_strategy._map_model_name("codellama") == "gpt-4"
        assert chat_strategy._map_model_name("mistral") == "gpt-3.5-turbo"
        
        # Test unmapped model
        assert chat_strategy._map_model_name("unknown") == "unknown"
    
    def test_empty_model_mappings(self):
        """Test translation with empty model mappings."""
        settings = Mock(spec=Settings)
        settings.model_mappings = {}
        
        strategy = ChatTranslationStrategy(settings)
        
        # All models should pass through unchanged
        assert strategy._map_model_name("llama3") == "llama3"
        assert strategy._map_model_name("gpt-4") == "gpt-4"


class TestMessageTranslation:
    """Test cases for individual message translation."""
    
    def test_translate_text_message(self, chat_strategy):
        """Test translating a simple text message."""
        ollama_msg = OllamaMessage(role="user", content="Hello world")
        
        medusa_msg = chat_strategy._translate_message_to_medusa(ollama_msg)
        
        assert medusa_msg.role == "user"
        assert medusa_msg.content == "Hello world"
    
    def test_translate_message_with_images(self, chat_strategy):
        """Test translating a message with images."""
        ollama_msg = OllamaMessage(
            role="user",
            content="Describe this image",
            images=["image1", "image2"]
        )
        
        medusa_msg = chat_strategy._translate_message_to_medusa(ollama_msg)
        
        assert medusa_msg.role == "user"
        assert isinstance(medusa_msg.content, list)
        assert len(medusa_msg.content) == 3  # 1 text + 2 images
        
        # Check text part
        assert medusa_msg.content[0]["type"] == "text"
        assert medusa_msg.content[0]["text"] == "Describe this image"
        
        # Check image parts
        assert medusa_msg.content[1]["type"] == "image_url"
        assert "image1" in medusa_msg.content[1]["image_url"]["url"]
        assert medusa_msg.content[2]["type"] == "image_url"
        assert "image2" in medusa_msg.content[2]["image_url"]["url"]


class TestOptionsMapping:
    """Test cases for Ollama options to OpenAI parameters mapping."""
    
    def test_apply_ollama_options(self, chat_strategy):
        """Test applying various Ollama options to Medusa request."""
        medusa_request = MedusaChatRequest(
            model="gpt-4",
            messages=[MedusaMessage(role="user", content="Hello")]
        )
        
        options = {
            "temperature": 0.8,
            "top_p": 0.95,
            "max_tokens": 200,
            "num_predict": 150,  # Should override max_tokens
            "stop": ["STOP", "END"],
            "frequency_penalty": 0.1,
            "presence_penalty": 0.2,
            "unknown_option": "ignored"  # Should be ignored
        }
        
        chat_strategy._apply_ollama_options(medusa_request, options)
        
        assert medusa_request.temperature == 0.8
        assert medusa_request.top_p == 0.95
        assert medusa_request.max_tokens == 150  # num_predict takes precedence
        assert medusa_request.stop == ["STOP", "END"]
        assert medusa_request.frequency_penalty == 0.1
        assert medusa_request.presence_penalty == 0.2
        assert not hasattr(medusa_request, "unknown_option")
    
    def test_apply_empty_options(self, chat_strategy):
        """Test applying empty options dictionary."""
        medusa_request = MedusaChatRequest(
            model="gpt-4",
            messages=[MedusaMessage(role="user", content="Hello")]
        )
        
        original_temperature = medusa_request.temperature
        
        chat_strategy._apply_ollama_options(medusa_request, {})
        
        # Should remain unchanged
        assert medusa_request.temperature == original_temperature
