"""
Translation service for converting between Ollama and OpenAI API formats.

This module implements the strategy pattern for translating requests and responses
between different API formats, with support for model name mapping.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any

from app.api.schemas.ollama_schemas import (
    OllamaChatRequest,
    OllamaChatResponse,
    OllamaResponseMessage,
    OllamaMessage,
)
from app.api.schemas.medusa_schemas import (
    MedusaChatRequest,
    MedusaChatResponse,
    MedusaMessage,
)
from app.core.config import Settings

logger = logging.getLogger(__name__)


class TranslationStrategy(ABC):
    """
    Abstract base class for translation strategies.
    
    Defines the interface for translating between different API formats.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize the translation strategy.
        
        Args:
            settings: Application settings containing model mappings
        """
        self.settings = settings
    
    @abstractmethod
    def translate_request(self, ollama_request: OllamaChatRequest) -> MedusaChatRequest:
        """
        Translate an Ollama request to Medusa format.
        
        Args:
            ollama_request: The Ollama chat request
            
        Returns:
            MedusaChatRequest: The translated request
        """
        pass
    
    @abstractmethod
    def translate_response(self, medusa_response: MedusaChatResponse, original_model: str) -> OllamaChatResponse:
        """
        Translate a Medusa response to Ollama format.
        
        Args:
            medusa_response: The Medusa chat response
            original_model: The original model name from the request
            
        Returns:
            OllamaChatResponse: The translated response
        """
        pass


class ChatTranslationStrategy(TranslationStrategy):
    """
    Translation strategy for chat completions.
    
    Handles translation between Ollama and OpenAI chat completion formats.
    """
    
    def translate_request(self, ollama_request: OllamaChatRequest) -> MedusaChatRequest:
        """
        Translate an Ollama chat request to Medusa/OpenAI format.
        
        Args:
            ollama_request: The Ollama chat request
            
        Returns:
            MedusaChatRequest: The translated request
        """
        logger.debug(f"Translating Ollama request for model: {ollama_request.model}")
        
        # Apply model name mapping
        mapped_model = self._map_model_name(ollama_request.model)
        logger.debug(f"Model mapped from '{ollama_request.model}' to '{mapped_model}'")
        
        # Translate messages
        medusa_messages = []
        for msg in ollama_request.messages:
            medusa_msg = self._translate_message_to_medusa(msg)
            medusa_messages.append(medusa_msg)
        
        # Build the Medusa request
        medusa_request = MedusaChatRequest(
            model=mapped_model,
            messages=medusa_messages,
            stream=ollama_request.stream or False,
        )
        
        # Add optional parameters from Ollama options
        if ollama_request.options:
            self._apply_ollama_options(medusa_request, ollama_request.options)
        
        logger.debug(f"Translated request: {len(medusa_messages)} messages, stream={medusa_request.stream}")
        return medusa_request
    
    def translate_response(self, medusa_response: MedusaChatResponse, original_model: str) -> OllamaChatResponse:
        """
        Translate a Medusa/OpenAI response to Ollama format.
        
        Args:
            medusa_response: The Medusa chat response
            original_model: The original model name from the request
            
        Returns:
            OllamaChatResponse: The translated response
        """
        logger.debug(f"Translating Medusa response for model: {original_model}")
        
        # Extract the first choice (Ollama doesn't support multiple choices)
        if not medusa_response.choices:
            raise ValueError("Medusa response contains no choices")
        
        choice = medusa_response.choices[0]
        
        # Create Ollama response message
        ollama_message = OllamaResponseMessage(
            role="assistant",
            content=choice.message.content or ""
        )
        
        # Create Ollama response
        ollama_response = OllamaChatResponse(
            model=original_model,  # Use original model name, not mapped
            created_at=datetime.fromtimestamp(medusa_response.created),
            message=ollama_message,
            done=True,
        )
        
        # Add usage information if available
        if medusa_response.usage:
            ollama_response.prompt_eval_count = medusa_response.usage.prompt_tokens
            ollama_response.eval_count = medusa_response.usage.completion_tokens
        
        logger.debug(f"Translated response: {len(ollama_message.content)} characters")
        return ollama_response
    
    def _map_model_name(self, ollama_model: str) -> str:
        """
        Map an Ollama model name to a backend model name.
        
        Args:
            ollama_model: The Ollama model name
            
        Returns:
            str: The mapped backend model name
        """
        mapped = self.settings.model_mappings.get(ollama_model, ollama_model)
        if mapped != ollama_model:
            logger.info(f"Model mapping: {ollama_model} -> {mapped}")
        return mapped
    
    def _translate_message_to_medusa(self, ollama_msg: OllamaMessage) -> MedusaMessage:
        """
        Translate an Ollama message to Medusa format.
        
        Args:
            ollama_msg: The Ollama message
            
        Returns:
            MedusaMessage: The translated message
        """
        # Handle multimodal content if images are present
        if ollama_msg.images:
            # Convert to multimodal format
            content_parts = [
                {"type": "text", "text": ollama_msg.content}
            ]
            for image in ollama_msg.images:
                content_parts.append({
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image}"}
                })
            content = content_parts
        else:
            content = ollama_msg.content
        
        return MedusaMessage(
            role=ollama_msg.role,
            content=content
        )
    
    def _apply_ollama_options(self, medusa_request: MedusaChatRequest, options: Dict[str, Any]) -> None:
        """
        Apply Ollama options to the Medusa request.
        
        Args:
            medusa_request: The Medusa request to modify
            options: Ollama options dictionary
        """
        # Map common Ollama options to OpenAI parameters
        option_mapping = {
            "temperature": "temperature",
            "top_p": "top_p",
            "max_tokens": "max_tokens",
            "num_predict": "max_tokens",  # Ollama's num_predict -> OpenAI's max_tokens
            "stop": "stop",
            "frequency_penalty": "frequency_penalty",
            "presence_penalty": "presence_penalty",
        }
        
        for ollama_key, openai_key in option_mapping.items():
            if ollama_key in options:
                setattr(medusa_request, openai_key, options[ollama_key])
                logger.debug(f"Applied option: {ollama_key}={options[ollama_key]} -> {openai_key}")


class TranslationService:
    """
    Main translation service that uses strategies for different translation types.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize the translation service.
        
        Args:
            settings: Application settings
        """
        self.settings = settings
        self.chat_strategy = ChatTranslationStrategy(settings)
    
    def translate_chat_request(self, ollama_request: OllamaChatRequest) -> MedusaChatRequest:
        """
        Translate an Ollama chat request to Medusa format.
        
        Args:
            ollama_request: The Ollama chat request
            
        Returns:
            MedusaChatRequest: The translated request
        """
        return self.chat_strategy.translate_request(ollama_request)
    
    def translate_chat_response(self, medusa_response: MedusaChatResponse, original_model: str) -> OllamaChatResponse:
        """
        Translate a Medusa chat response to Ollama format.
        
        Args:
            medusa_response: The Medusa chat response
            original_model: The original model name from the request
            
        Returns:
            OllamaChatResponse: The translated response
        """
        return self.chat_strategy.translate_response(medusa_response, original_model)
